import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/class_model.dart';
import '../enums/classroom_type.dart';

/// Header widget displaying classroom information in detail screen
class ClassroomHeader extends StatelessWidget {
  /// The classroom data to display
  final ClassModel classroom;

  const ClassroomHeader({super.key, required this.classroom});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Top row with avatar and basic info
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAvatar(),
            SizedBox(width: 16.w),
            // Basic info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Classroom name
                  Text(
                    classroom.name,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 4.h),

                  // Subject (if available)
                  if (classroom.subject != null)
                    Text(
                      classroom.subject!,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.8,
                        ),
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                  // Description (if available)
                  if (classroom.description != null) ...[
                    SizedBox(height: 8.h),
                    Text(
                      classroom.description!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  SizedBox(height: 16.h),

                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: classroom.type.defaultColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: classroom.type.defaultColor.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    child: Text(
                      classroom.type.label,
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: classroom.type.defaultColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Instructor/Admin information
        if (classroom.instructorDisplayName != null) ...[
          Row(
            children: [
              Icon(
                classroom.teacherId != null ? Symbols.school : Symbols.person,
                size: 16.sp,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              SizedBox(width: 8.w),
              Text(
                '${classroom.teacherId != null ? 'Instructor' : 'Admin'}: ${classroom.instructorDisplayName}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
        ],

        // Additional info row
        Row(
          children: [
            // Student count
            _buildInfoItem(
              icon: Symbols.people,
              label: '${classroom.studentIds.length} students',
              theme: theme,
            ),

            SizedBox(width: 24.w),

            // Grade and section (if available)
            if (classroom.gradeLevel != null) ...[
              _buildInfoItem(
                icon: Symbols.grade,
                label:
                    'Grade ${classroom.gradeLevel}${classroom.section ?? ''}',
                theme: theme,
              ),
              SizedBox(width: 24.w),
            ],

            // Primary class indicator
            if (classroom.isPrimaryClass)
              _buildInfoItem(
                icon: Symbols.star,
                label: 'Primary Eligible',
                theme: theme,
                color: theme.colorScheme.primary,
              ),
          ],
        ),
      ],
    );
  }

  /// Build avatar/icon section
  Widget _buildAvatar() {
    // If profile picture is available, use it
    if (classroom.profilePictureUrl != null) {
      return CircleAvatar(
        radius: 32.r,
        backgroundImage: NetworkImage(classroom.profilePictureUrl!),
        backgroundColor: classroom.type.defaultColor.withValues(alpha: 0.1),
      );
    }

    // Otherwise, use icon or default classroom type icon
    return CircleAvatar(
      radius: 32.r,
      backgroundColor: classroom.type.defaultColor.withValues(alpha: 0.1),
      child: Icon(
        classroom.type.icon,
        size: 32.sp,
        color: classroom.type.defaultColor,
      ),
    );
  }

  /// Build info item with icon and label
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required ThemeData theme,
    Color? color,
  }) {
    final effectiveColor =
        color ?? theme.colorScheme.onSurface.withValues(alpha: 0.6);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16.sp, color: effectiveColor),
        SizedBox(width: 4.w),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: effectiveColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
