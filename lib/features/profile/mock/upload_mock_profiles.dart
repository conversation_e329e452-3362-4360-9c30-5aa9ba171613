import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/enums/auth_enums.dart';
import '../models/profile_model.dart';
import 'mock_profiles.dart';

/// Service for uploading mock profile data to Firestore
class UploadMockProfiles {
  static final UploadMockProfiles _instance = UploadMockProfiles._internal();
  factory UploadMockProfiles() => _instance;
  UploadMockProfiles._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection name
  static const String _profilesCollection = 'user_profiles';

  /// Upload all mock profiles to Firestore
  Future<void> uploadAllProfiles() async {
    try {
      _logger.i('Starting upload of ${mockProfilesList.length} mock profiles');

      final batch = _firestore.batch();
      int batchCount = 0;
      const batchSize = 500; // Firestore batch limit

      for (final profile in mockProfilesList) {
        final docRef = _firestore
            .collection(_profilesCollection)
            .doc(profile.id);
        batch.set(docRef, profile.toJson());
        batchCount++;

        // Commit batch if we reach the limit
        if (batchCount >= batchSize) {
          await batch.commit();
          _logger.i('Committed batch of $batchCount profiles');
          batchCount = 0;
        }
      }

      // Commit remaining profiles
      if (batchCount > 0) {
        await batch.commit();
        _logger.i('Committed final batch of $batchCount profiles');
      }

      _logger.i(
        'Successfully uploaded all ${mockProfilesList.length} mock profiles',
      );
    } catch (e) {
      _logger.e('Error uploading mock profiles: $e');
      throw Exception('Failed to upload mock profiles: $e');
    }
  }

  /// Upload a single profile to Firestore
  Future<void> uploadProfile(ProfileModel profile) async {
    try {
      _logger.i('Uploading profile for user: ${profile.fullName}');

      await _firestore
          .collection(_profilesCollection)
          .doc(profile.id)
          .set(profile.toJson());

      _logger.i('Successfully uploaded profile for: ${profile.fullName}');
    } catch (e) {
      _logger.e('Error uploading profile: $e');
      throw Exception('Failed to upload profile: $e');
    }
  }

  /// Clear all profiles from Firestore
  Future<void> clearAllProfiles() async {
    try {
      _logger.i('Clearing all profiles from Firestore');

      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .get();
      final batch = _firestore.batch();

      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      _logger.i('Successfully cleared ${querySnapshot.docs.length} profiles');
    } catch (e) {
      _logger.e('Error clearing profiles: $e');
      throw Exception('Failed to clear profiles: $e');
    }
  }

  /// Check if profiles already exist in Firestore
  Future<bool> profilesExist() async {
    try {
      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .limit(1)
          .get();

      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      _logger.e('Error checking if profiles exist: $e');
      return false;
    }
  }

  /// Get profile count from Firestore
  Future<int> getProfileCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_profilesCollection)
          .get();
      return querySnapshot.docs.length;
    } catch (e) {
      _logger.e('Error getting profile count: $e');
      return 0;
    }
  }

  /// Upload profiles with progress callback
  Future<void> uploadProfilesWithProgress({
    required Function(int current, int total) onProgress,
  }) async {
    try {
      _logger.i(
        'Starting upload of ${mockProfilesList.length} mock profiles with progress tracking',
      );

      for (int i = 0; i < mockProfilesList.length; i++) {
        final profile = mockProfilesList[i];
        await uploadProfile(profile);
        onProgress(i + 1, mockProfilesList.length);
      }

      _logger.i('Successfully uploaded all profiles with progress tracking');
    } catch (e) {
      _logger.e('Error uploading profiles with progress: $e');
      throw Exception('Failed to upload profiles: $e');
    }
  }

  /// Get mock profile statistics
  Map<String, dynamic> getMockProfileStats() {
    final stats = <String, dynamic>{};

    // Count by user type
    final userTypeCounts = <String, int>{};
    for (final profile in mockProfilesList) {
      final type = profile.userType.displayName;
      userTypeCounts[type] = (userTypeCounts[type] ?? 0) + 1;
    }
    stats['userTypeCounts'] = userTypeCounts;

    // Class enrollment statistics
    final profilesWithClasses = mockProfilesList
        .where((p) => p.classIds.isNotEmpty)
        .length;
    final profilesWithMultipleClasses = mockProfilesList
        .where((p) => p.classIds.length > 1)
        .length;

    stats['totalProfiles'] = mockProfilesList.length;
    stats['profilesWithClasses'] = profilesWithClasses;
    stats['profilesWithMultipleClasses'] = profilesWithMultipleClasses;
    stats['percentageWithClasses'] =
        (profilesWithClasses / mockProfilesList.length * 100).round();
    stats['percentageWithMultipleClasses'] =
        (profilesWithMultipleClasses / mockProfilesList.length * 100).round();

    // Subject statistics
    final allSubjects = <String>{};
    for (final profile in mockProfilesList) {
      allSubjects.addAll(profile.subjects);
    }
    stats['uniqueSubjects'] = allSubjects.length;

    // School statistics
    final allSchools = <String>{};
    for (final profile in mockProfilesList) {
      if (profile.school != null) {
        allSchools.add(profile.school!);
      }
    }
    stats['uniqueSchools'] = allSchools.length;

    return stats;
  }

  /// Validate mock data meets requirements
  bool validateMockData() {
    final stats = getMockProfileStats();

    // Check if we have at least 30 profiles
    if (stats['totalProfiles'] < 30) {
      _logger.w(
        'Not enough profiles: ${stats['totalProfiles']} (required: 30)',
      );
      return false;
    }

    // Check if 90% have at least one class enrollment
    if (stats['percentageWithClasses'] < 90) {
      _logger.w(
        'Not enough profiles with classes: ${stats['percentageWithClasses']}% (required: 90%)',
      );
      return false;
    }

    // Check if 50% have multiple class enrollments
    if (stats['percentageWithMultipleClasses'] < 50) {
      _logger.w(
        'Not enough profiles with multiple classes: ${stats['percentageWithMultipleClasses']}% (required: 50%)',
      );
      return false;
    }

    _logger.i('Mock data validation passed');
    return true;
  }
}
