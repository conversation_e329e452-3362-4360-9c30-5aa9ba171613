import '../../../core/enums/auth_enums.dart';

/// Model representing extended profile information for a user
class ProfileModel {
  /// Unique identifier for the user (Firebase UID)
  final String id;

  /// User's full name
  final String fullName;

  /// User's email address
  final String email;

  /// Type of user (student, parent, teacher, admin, other)
  final UserType userType;

  /// List of class IDs the user is enrolled in or associated with
  final List<String> classIds;

  /// ID of the user's primary classroom (determines grade, section, etc.)
  /// Must be a classroom where isPrimaryClass is true
  final String? primaryClassId;

  /// Profile image URL
  final String? profileImageUrl;

  /// User's grade level (for students)
  final String? grade;

  /// User's school name
  final String? school;

  /// Student ID (for students)
  final String? studentId;

  /// User's bio or description
  final String? bio;

  /// List of subjects the user is associated with
  final List<String> subjects;

  /// User's phone number
  final String? phoneNumber;

  /// User's date of birth
  final DateTime? dateOfBirth;

  /// User's address
  final String? address;

  /// Emergency contact information
  final String? emergencyContact;

  /// Emergency contact phone number
  final String? emergencyContactPhone;

  /// Parent/Guardian information (for students)
  final String? parentGuardianName;

  /// Parent/Guardian phone number (for students)
  final String? parentGuardianPhone;

  /// Parent/Guardian email (for students)
  final String? parentGuardianEmail;

  /// When the profile was created
  final DateTime? createdAt;

  /// When the profile was last updated
  final DateTime? updatedAt;

  /// Whether the profile is active
  final bool isActive;

  /// Additional profile metadata
  final Map<String, dynamic>? metadata;

  const ProfileModel({
    required this.id,
    required this.fullName,
    required this.email,
    required this.userType,
    this.classIds = const [],
    this.primaryClassId,
    this.profileImageUrl,
    this.grade,
    this.school,
    this.studentId,
    this.bio,
    this.subjects = const [],
    this.phoneNumber,
    this.dateOfBirth,
    this.address,
    this.emergencyContact,
    this.emergencyContactPhone,
    this.parentGuardianName,
    this.parentGuardianPhone,
    this.parentGuardianEmail,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.metadata,
  });

  /// Create a ProfileModel from JSON
  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      id: json['id'] as String,
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      userType: json['userType'] != null
          ? UserTypeExtension.fromString(json['userType'] as String)
          : UserType.student,
      classIds: json['classIds'] != null
          ? List<String>.from(json['classIds'] as List)
          : const [],
      primaryClassId: json['primaryClassId'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      grade: json['grade'] as String?,
      school: json['school'] as String?,
      studentId: json['studentId'] as String?,
      bio: json['bio'] as String?,
      subjects: json['subjects'] != null
          ? List<String>.from(json['subjects'] as List)
          : const [],
      phoneNumber: json['phoneNumber'] as String?,
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      address: json['address'] as String?,
      emergencyContact: json['emergencyContact'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      parentGuardianName: json['parentGuardianName'] as String?,
      parentGuardianPhone: json['parentGuardianPhone'] as String?,
      parentGuardianEmail: json['parentGuardianEmail'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert ProfileModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'email': email,
      'userType': userType.value,
      'classIds': classIds,
      'primaryClassId': primaryClassId,
      'profileImageUrl': profileImageUrl,
      'grade': grade,
      'school': school,
      'studentId': studentId,
      'bio': bio,
      'subjects': subjects,
      'phoneNumber': phoneNumber,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'address': address,
      'emergencyContact': emergencyContact,
      'emergencyContactPhone': emergencyContactPhone,
      'parentGuardianName': parentGuardianName,
      'parentGuardianPhone': parentGuardianPhone,
      'parentGuardianEmail': parentGuardianEmail,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// Create a copy of this ProfileModel with updated fields
  ProfileModel copyWith({
    String? id,
    String? fullName,
    String? email,
    UserType? userType,
    List<String>? classIds,
    String? primaryClassId,
    String? profileImageUrl,
    String? grade,
    String? school,
    String? studentId,
    String? bio,
    List<String>? subjects,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? address,
    String? emergencyContact,
    String? emergencyContactPhone,
    String? parentGuardianName,
    String? parentGuardianPhone,
    String? parentGuardianEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return ProfileModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      userType: userType ?? this.userType,
      classIds: classIds ?? this.classIds,
      primaryClassId: primaryClassId ?? this.primaryClassId,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      grade: grade ?? this.grade,
      school: school ?? this.school,
      studentId: studentId ?? this.studentId,
      bio: bio ?? this.bio,
      subjects: subjects ?? this.subjects,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      parentGuardianName: parentGuardianName ?? this.parentGuardianName,
      parentGuardianPhone: parentGuardianPhone ?? this.parentGuardianPhone,
      parentGuardianEmail: parentGuardianEmail ?? this.parentGuardianEmail,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileModel &&
        other.id == id &&
        other.fullName == fullName &&
        other.email == email &&
        other.userType == userType &&
        other.classIds == classIds &&
        other.primaryClassId == primaryClassId &&
        other.profileImageUrl == profileImageUrl &&
        other.grade == grade &&
        other.school == school &&
        other.studentId == studentId &&
        other.bio == bio &&
        other.subjects == subjects &&
        other.phoneNumber == phoneNumber &&
        other.dateOfBirth == dateOfBirth &&
        other.address == address &&
        other.emergencyContact == emergencyContact &&
        other.emergencyContactPhone == emergencyContactPhone &&
        other.parentGuardianName == parentGuardianName &&
        other.parentGuardianPhone == parentGuardianPhone &&
        other.parentGuardianEmail == parentGuardianEmail &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      fullName,
      email,
      userType,
      Object.hashAll(classIds),
      primaryClassId,
      profileImageUrl,
      grade,
      school,
      studentId,
      bio,
      Object.hashAll(subjects),
      phoneNumber,
      dateOfBirth,
      address,
      emergencyContact,
      emergencyContactPhone,
      parentGuardianName,
      parentGuardianPhone,
      parentGuardianEmail,
    );
  }

  @override
  String toString() {
    return 'ProfileModel(id: $id, fullName: $fullName, email: $email, userType: ${userType.displayName})';
  }
}
